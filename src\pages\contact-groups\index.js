import SearchIcon from "@mui/icons-material/Search";
import {
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  InputAdornment,
  InputLabel,
  Menu,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import CustomChip from "src/@core/components/mui/chip";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";

import axios from "axios";
import FallbackSpinner from "src/@core/components/spinner";
import ActivateDialog from "./ActivateDialog";
import ContactGroupDetails from "./ContactGroupDetails";
import DeleteDialog from "./DeleteDialog";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import IconButton from "@mui/material/IconButton";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const ContactGroupActions = () => {
  const [openDialog, setOpenDialog] = useState(false);
  const [groupsList, setGroupsList] = useState([]);
  const [currentRow, setCurrentRow] = useState();
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [rowCount, setRowCount] = useState(0);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openActivateDialog, setOpenActivateDialog] = useState(false);
  const [actionMenuAnchorEl, setActionMenuAnchorEl] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const [menu, setMenu] = useState(null);
  const [whatsappOpenDialog, setWhatsappOpenDialog] = useState(false);
  const [sendMessageDialog, setSendMessageDialog] = useState(false);
  const [templates, setTemplates] = useState([]);
  const [selectedTemplateName, setSelectedTemplateName] = useState("");
  const [customFieldValue, setCustomFieldValue] = useState(""); // New state variable for text field
  const [messageStatus, setMessageStatus] = useState(null);
  const [dialogMessage, setDialogMessage] = useState("");
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [noResultsMessage, setNoResultsMessage] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [sendingMessage, setSendingMessage] = useState(false);

  const [tenantsList, setTenantsList] = useState([]);

  const [loading, setLoading] = useState(true);

  const { reset, control } = useForm();

  const { contactGroupsData, contactGroupsDataDetails, setContactGroupsDataDetails,setContactGroupsData,user,sendWatiMessage } =
    useContext(AuthContext);

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    reset();
    setContactGroupsDataDetails(null);
    setOpenDialog(false);
  };

  const handleCloseEditDialog = () => {
    setEditDialogOpen(false);
  };

  const handleSelectionModelChange = (newSelectionModel) => {
    const selectedRowData = newSelectionModel.map((id) =>
      groupsList.find((row) => row.id === id)
    );
    setSelectedRows(selectedRowData);
  };

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const handleOpenActionMenu = (event) => {
    setActionMenuAnchorEl(event.currentTarget);
  };


   const token =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pKpfhwXgjSNRAJt2XozZI9_ezpj025J36UBObgQDf5k";
    useEffect(() => {
      const fetchTemplates = async () => {
        try {
          const response = await fetch(
            "https://live-mt-server.wati.io/321589/api/v1/getMessageTemplates",
            {
              method: "GET",
              headers: {
                Authorization: `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            }
          );
          const data = await response.json();
  
          const templateData = data.messageTemplates.map((template) => ({
            key: template?.elementName,
            value: template?.id,
            parameters: template.parameters || [], // Keep additional data if needed
            status: template.status,
          }));
          const filteredTemplates = templateData.filter(
            (template) => template.status !== "DELETED"
          );
  
          setTemplates(filteredTemplates);
        } catch (error) {}
      };
  
      fetchTemplates();
    }, [token]);

   useEffect(() => {
      axios({
        method: "get",
        url: getUrl(authConfig.organisationsEndpoint) + "/TENANT",
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setTenantsList(
            res.data.map((item) => ({
              value: item.id,
              key: item.name,
            }))
          );
        })
        .catch(() => {
          setTenantsList([]);
        });
    }, []);

  const handleCloseActionMenu = () => {
    setActionMenuAnchorEl(null);
  };

  const handleSendWhatsAppMessages = () => {
    setWhatsappOpenDialog(true);
    handleCloseActionMenu();
  };

  const handleSendMessageClick = () => {
    setSendMessageDialog(true);
    handleCloseMenuItems();
  };

  const handleCloseSendMessageDialog = () => {
    setSendMessageDialog(false);
    setSelectedTemplateName("");
    setSendingMessage(false);
  };

  const handleSendMessage = async () => {
    if (!selectedTemplateName || !currentRow) {
      setDialogMessage("Please select a template");
      setMessageStatus("error");
      return;
    }

    setSendingMessage(true);

    try {
      // Get all contacts from the current group
      const contacts = currentRow.donorResponseList || [];

      if (contacts.length === 0) {
        setDialogMessage("No contacts found in this group");
        setMessageStatus("error");
        setSendingMessage(false);
        return;
      }

      const successfulDeliveries = [];
      const unsuccessfulDeliveries = [];

      // Send message to each contact in the group
      for (const contact of contacts) {
        if (contact.mobileNumber) {
          const parameters = [
            { name: "name", value: contact.name || "Member" },
            { name: "group_name", value: currentRow.name || "Group" }
          ];

          await sendWatiMessage(
            contact.mobileNumber,
            selectedTemplateName,
            selectedTemplateName,
            parameters,
            (error) => {
              unsuccessfulDeliveries.push({
                name: contact.name,
                mobileNumber: contact.mobileNumber,
                reason: "Failed to send message"
              });
            },
            (data) => {
              if (data.validWhatsAppNumber) {
                successfulDeliveries.push({
                  name: contact.name,
                  mobileNumber: contact.mobileNumber
                });
              } else {
                unsuccessfulDeliveries.push({
                  name: contact.name,
                  mobileNumber: contact.mobileNumber,
                  reason: "Invalid or not registered with WhatsApp"
                });
              }
            }
          );
        } else {
          unsuccessfulDeliveries.push({
            name: contact.name,
            reason: "Missing mobile number"
          });
        }
      }

      // Prepare result message
      let message = "";

      if (successfulDeliveries.length > 0) {
        const sentList = successfulDeliveries
          .map((entry) => `${entry.name} (${entry.mobileNumber})`)
          .join(", ");
        message += `Message sent successfully to: ${sentList}.<br><br>`;
      }

      if (unsuccessfulDeliveries.length > 0) {
        const notSentList = unsuccessfulDeliveries
          .map((entry) => `${entry.name} (${entry.mobileNumber || "N/A"}) - ${entry.reason}`)
          .join(", ");
        message += `Message not sent to: ${notSentList}`;
      }

      setDialogMessage(message);
      setMessageStatus("info");
      handleCloseSendMessageDialog();

    } catch (error) {
      setDialogMessage("Error sending messages. Please try again.");
      setMessageStatus("error");
    } finally {
      setSendingMessage(false);
    }
  };
 

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const columns = [
    {
      flex: 0.43,
      field: "name",
      minWidth: 90,
      headerName: "Group Name",
    },
    {
      flex: 0.43,
      field: "description",
      minWidth: 90,
      headerName: "Description",
    },
    user?.organisationCategory === "SUPER_ADMIN"
      ? {
      flex: 0.43,
      field: "orgId",
      minWidth: 90,
      headerName: "Organisation Name",
        renderCell: (params) => {
        const org = tenantsList?.find(
          (item) => item?.value === params?.row?.orgId
        );
        return (
          <Tooltip title={org ? org?.key : ""}>
            <span>{org ? org?.key : ""}</span>
          </Tooltip>
        );
      },
    }:null,
    {
      flex: 0.2,
      field: "donorResponseList",
      minWidth: 110,
      headerName: "Count of group members",
      valueGetter: (params) => params.row.donorResponseList?.length || 0,
      align: "center",
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.13,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      flex: 0.05,
      field: "actions",
      headerName: "Actions",
      sortable: false,
      minWidth: 100,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setContactGroupsData({
            ...contactGroupsData,
            id: row.id,
          });
        };

        const onClickViewProfile = () => {
          setOpenDialog(true);
          handleCloseMenuItems();
        };
        const onClickDeleteProfile = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems();
        };

        const onClickActivateProfile = () => {
          setOpenActivateDialog(true);
          handleCloseMenuItems();
        };

        const onClickSendMessage = () => {
          handleSendMessageClick();
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              keepMounted
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              {currentRow?.isActive ? (
                <MenuItem onClick={onClickDeleteProfile}>DeActivate</MenuItem>
              ) : (
                <MenuItem onClick={onClickActivateProfile}>Activate</MenuItem>
              )}
              <MenuItem onClick={onClickSendMessage}>
                Send message via WhatsApp
              </MenuItem>
              {/* <MenuItem
                onClick={handleSendWhatsAppMessages}
                disabled={selectedRows?.length === 0}
              >
                Send WhatsApp Messages
              </MenuItem> */}
            </Menu>
          </div>
        );
      },
    },
  ].filter(Boolean);

  const fetchContactGroups = async (
    currentPage,
    currentPageSize
  ) => {
    const url = getUrl(authConfig.donorGroupsEndpoint) + "/all";

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setGroupsList(response.data.donorGroupsResponses || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchContactGroups(page, pageSize);
  }, [page, pageSize]);

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchContactGroups(page,pageSize);
  };

  const handleCloseActivateDialog = () => {
    setOpenActivateDialog(false);
    fetchContactGroups(page,pageSize);
  };

  const handlePageChange = (newPage) => {
    setPage(newPage + 1);
  };

  const handlePageSizeChange = (params) => {
    setPageSize(params.pageSize);
    setPage(1);
  };

  const sendMessagesToUsersWithNumbers = async (rows) => {
    const templateName = selectedTemplateName;
    const broadcastName = selectedTemplateName;
    let sentMessages = [];
    let notSentMessages = [];
    let invalidWhatsAppNumbers = [];

    for (const row of rows) {
      if (row.mobileNumber) {
        const params = [
          { name: "name", value: row.firstName },
          { name: "society_name", value: selectedSociety },
          {
            name: "service_requirement",
            value: getServiceTypeValue(requirementName),
          },
          { name: "measurements", value: measurements },
          { name: "location", value: location },
          { name: "slot_url", value: googleFormLink },
          { name: "image_url", value: imageUrl },
        ];

        await sendWatiMessage(
          row.mobileNumber,
          templateName,
          broadcastName,
          params,
          (error) => {
            notSentMessages.push({
              name: row.firstName,
              mobileNumber: row.mobileNumber,
              reason: "Failed to send message",
            });
          },
          (data) => {
            if (data.validWhatsAppNumber) {
              sentMessages.push({
                name: row.firstName,
                mobileNumber: row.mobileNumber,
              });
            } else {
              invalidWhatsAppNumbers.push({
                name: row.firstName,
                mobileNumber: row.mobileNumber,
                reason: "Invalid or not registered with WhatsApp",
              });
            }
          }
        );
      } else {
        notSentMessages.push({
          name: row.firstName,
          reason: "Missing mobile number",
        });
      }
    }

    const sentList = sentMessages
      .map((entry) => `${entry.name} (Mobile: ${entry.mobileNumber})`)
      .join(", ");

    const notSentList = notSentMessages
      .map(
        (entry) =>
          `${entry.name} (Mobile: ${entry.mobileNumber || "N/A"}) - ${
            entry.reason
          }`
      )
      .join(", ");

    const invalidList = invalidWhatsAppNumbers
      .map(
        (entry) =>
          `${entry.name} (Mobile: ${entry.mobileNumber}) - ${entry.reason}`
      )
      .join(", ");

    let dialogMessage = "";

    if (notSentList.length > 0) {
      dialogMessage += `Messages could not be sent to: ${notSentList}. `;
    }

    if (invalidList.length > 0) {
      dialogMessage += `Numbers not registered with WhatsApp: ${invalidList}.`;
    }

    setDialogMessage(dialogMessage);
    setMessageStatus("info");
    setWhatsappOpenDialog(false); // Close the WhatsApp dialog after sending messages
  };

  const sendMessages = async () => {
    if (!selectedTemplateName || selectedRows.length === 0) {
      setDialogMessage("Template or WhatsApp number is missing");
      setMessageStatus("error");
      return;
    }

    const successfulDeliveries = [];
    const unsuccessfulDeliveries = [];

    const receivers = selectedRows.flatMap((row) =>
      (row.contactList || [])
        .filter((contact) => contact.mobileNumber)
        .map((contact) => ({
          firstName: contact.firstName,
          whatsappNumber: contact.mobileNumber,
          customParams: [
            { name: "name", value: contact.firstName },
            { name: "society_name", value: customFieldValue || "" },
            {
              name: "service_requirement",
              value: "row.serviceRequirement" || "",
            },
            { name: "measurements", value: row.measurements || "" },
          ],
        }))
    );

    for (const receiver of receivers) {
      await sendWatiMessage(
        receiver.whatsappNumber,
        selectedTemplateName,
        selectedTemplateName,
        receiver.customParams,
        (errors) => {
          if (
            errors.invalidWhatsappNumber &&
            errors.invalidWhatsappNumber.length > 0
          ) {
            unsuccessfulDeliveries.push({
              firstName: receiver.firstName,
              whatsappNumber: receiver.whatsappNumber,
            });
          }
        },
        (data) => {
          if (data.validWhatsAppNumber) {
            successfulDeliveries.push({
              firstName: receiver.firstName,
              whatsappNumber: receiver.whatsappNumber,
            });
          } else {
            unsuccessfulDeliveries.push({
              firstName: receiver.firstName,
              whatsappNumber: receiver.whatsappNumber,
            });
          }
        }
      );
    }

    let message = "";

    const sentMessage = successfulDeliveries
      .map((entry) => `${entry.firstName} (${entry.whatsappNumber})`)
      .join(", ");

    const notSentNamesAndNumbers = unsuccessfulDeliveries
      .map((entry) => `${entry.firstName} (${entry.whatsappNumber})`)
      .join(", ");

    if (successfulDeliveries.length > 0) {
      message += `Message sent to: ${sentMessage}.<br><br>`;
    }

    if (unsuccessfulDeliveries.length > 0) {
      const notSentMessage = `Message not sent to: ${notSentNamesAndNumbers} because these numbers are not registered with WhatsApp.`;
      message += `${notSentMessage}`;
    }

    setDialogMessage(message);
    setMessageStatus("info");
    setWhatsappOpenDialog(false); // Close the WhatsApp dialog after sending messages
  };

  const handleCloseWhatsappDialog = () => {
    setWhatsappOpenDialog(false);
    setSelectedTemplateName("");
    setCustomFieldValue(""); // Reset the custom field value
    setIsButtonDisabled(true);
  };

  const handleMessageStatusClose = () => {
    setMessageStatus(null);
    handleCloseWhatsappDialog(); // Close the WhatsApp dialog if it is open
  };

  return (
    <>
      <Grid>
        <div>
          <CardContent>
            <Box
              sx={{
                px: 6,
                rowGap: 2,
                columnGap: 4,
                display: "flex",
                flexWrap: "wrap",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                  <Typography variant="h6" fontWeight={"600"}>
                    List of Donor Groups
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={8}>
                  <Grid container justifyContent="flex-end">
                    {/* <Grid item xs={12} sm="auto" sx={{ mr: "8px" }}>
                      <FormControl fullWidth>
                        <Controller
                          name="mainSearch"
                          control={control}
                          render={({ field: { onChange } }) => (
                            <TextField
                              size="small"
                              id="mainSearch"
                              placeholder="Search by group name"
                              value={keyword}
                              onChange={(e) => {
                                const value = e.target.value; 
                                onChange(value);
                                setKeyword(value);
                                setSearchKeyword(value);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  setSearchKeyword(keyword);
                                }
                              }}
                              sx={{
                                "& .MuiInputBase-root": {
                                  fontSize: "0.9rem",
                                  borderRadius: "5px",
                                  backgroundColor: "white",
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="start">
                                    <SearchIcon
                                      sx={{
                                        cursor: "pointer",
                                        marginRight: "-15px",
                                      }}
                                      onClick={() => {
                                        setSearchKeyword(keyword);
                                      }}
                                    />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid> */}
                    <Grid
                      item
                      xs="auto"
                      sm="auto"
                      sx={{ mr: "6px", ml: "6px" }}
                    >
                    </Grid>
                    <Grid item xs="auto" sm="auto" sx={{ mr: "8px" }}>
                      <Button
                        onClick={handleOpenDialog}
                        variant="contained"
                        sx={{ textTransform: "none" }}
                      >
                        + Add
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Box>
          </CardContent>

          <Divider />
          <CardContent>
            <div style={{ height: 460, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={groupsList}
                  columns={columns}
                  checkboxSelection
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                  onSelectionModelChange={handleSelectionModelChange}
                  components={{
                    NoRowsOverlay: () => (
                      <Typography
                        variant="body1"
                        align="center"
                        sx={{ marginTop: "120px" }}
                      >
                        {groupsList?.length === 0 ? "No Data" : "No Rows"}
                      </Typography>
                    ),
                  }}
                />
              )}
            </div>
          </CardContent>
        </div>
        <DeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
        />
        <ActivateDialog
          open={openActivateDialog}
          onClose={handleCloseActivateDialog}
          data={currentRow}
        />
        <ContactGroupDetails
          open={openDialog}
          onClose={handleCloseDialog}
          fetchContactGroups={fetchContactGroups}
          tenantsList={tenantsList}
          formData={contactGroupsDataDetails}
        />
      </Grid>

      {/* WhatsApp Dialog */}
      <Dialog
        open={whatsappOpenDialog}
        onClose={handleCloseWhatsappDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            width: "400px",
            height: "300px",
          },
        }}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            marginLeft: { xl: 1.5, lg: 1.5, md: 1.5, sm: 1.5, xs: 1.5 },
          }}
          textAlign={"center"}
        >
          Send Message via WhatsApp
        </DialogTitle>

        <Box sx={{ position: "absolute", top: "8px", right: "24px" }}>
          <IconButton
            size="small"
            onClick={handleCloseWhatsappDialog}
            sx={{
              // p: "0.438rem",
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel id="select-label">Select a Template</InputLabel>
            <Select
              labelId="select-label"
              value={selectedTemplateName}
              onChange={(event) => {
                setSelectedTemplateName(event.target.value);

                setIsButtonDisabled(!event.target.value);
              }}
              label="Select a Template"
              size="small"
            >
              {templates?.map((template) => (
                <MenuItem key={template.id} value={template.name}>
                  {template.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <TextField
              size="small"
              id="custom-field"
              placeholder="Enter custom message"
              value={customFieldValue}
              onChange={(e) => setCustomFieldValue(e.target.value)}
            />
          </FormControl>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            marginRight: { xl: 4, lg: 3.5, md: 3.5, sm: 3.5, xs: 3.5 },
          }}
        >
          <Button onClick={handleCloseWhatsappDialog}>Cancel</Button>
          <Button
            onClick={sendMessages}
            disabled={isButtonDisabled}
            variant="contained"
            color="primary"
          >
            Send Messages
          </Button>
        </DialogActions>
      </Dialog>

      {/* Message Status Dialog */}
      <Dialog
        open={messageStatus !== null}
        onClose={handleMessageStatusClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.text.primary.main,
          },
        }}
      >
        <Box
          sx={{
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
              fontWeight={"bold"}
              dangerouslySetInnerHTML={{ __html: dialogMessage }}
            />
          </DialogContent>
          <DialogActions
            sx={{
              display: "flex",
              justifyContent: "center",
            }}
          >
            <Button onClick={handleMessageStatusClose} color="primary">
              OK
            </Button>
          </DialogActions>
        </Box>
      </Dialog>

      {/* Send Message Dialog */}
      <Dialog
        open={sendMessageDialog}
        onClose={handleCloseSendMessageDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            width: "400px",
            height: "250px",
          },
        }}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            marginLeft: { xl: 1.5, lg: 1.5, md: 1.5, sm: 1.5, xs: 1.5 },
          }}
          textAlign={"center"}
        >
          Send Message via WhatsApp
        </DialogTitle>

        <Box sx={{ position: "absolute", top: "8px", right: "24px" }}>
          <IconButton
            size="small"
            onClick={handleCloseSendMessageDialog}
            sx={{
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>

        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel id="template-select-label">Select a Template</InputLabel>
            <Select
              labelId="template-select-label"
              value={selectedTemplateName}
              onChange={(event) => {
                setSelectedTemplateName(event.target.value);
              }}
              label="Select a Template"
              size="small"
            >
              {templates?.map((template) => (
                <MenuItem key={template.value} value={template.key}>
                  {template.key}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>

        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            marginRight: { xl: 4, lg: 3.5, md: 3.5, sm: 3.5, xs: 3.5 },
          }}
        >
          <Button onClick={handleCloseSendMessageDialog}>Cancel</Button>
          <Button
            onClick={handleSendMessage}
            disabled={!selectedTemplateName || sendingMessage}
            variant="contained"
            color="primary"
          >
            {sendingMessage ? "Sending..." : "Send"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ContactGroupActions;
